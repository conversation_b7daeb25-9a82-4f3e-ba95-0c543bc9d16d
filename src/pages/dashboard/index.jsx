import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Header from '../../components/ui/Header';
import Breadcrumbs from '../../components/ui/Breadcrumbs';
import QuickActionSidebar from '../../components/ui/QuickActionSidebar';
import QuickStatsCard from './components/QuickStatsCard';
import DocumentLibrary from './components/DocumentLibrary';
import QuickActions from './components/QuickActions';
import CreditUsageMeter from './components/CreditUsageMeter';
import CollaborationNotifications from './components/CollaborationNotifications';
import ActivityFeed from './components/ActivityFeed';
import Icon from '../../components/AppIcon';

const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);

  // Mock data
  const mockDocuments = [
    {
      id: 1,
      title: "AI-Powered Marketing Strategy for Tech Startups",
      type: "business",
      status: "completed",
      createdAt: "2024-01-15T10:30:00Z",
      pageCount: 45,
      thumbnail: "https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Sarah Johnson", email: "<EMAIL>" },
        { name: "Mike Chen", email: "<EMAIL>" }
      ]
    },
    {
      id: 2,
      title: "Research Paper: Machine Learning in Healthcare",
      type: "academic",
      status: "draft",
      createdAt: "2024-01-14T14:20:00Z",
      pageCount: 28,
      thumbnail: "https://images.pexels.com/photos/3825581/pexels-photo-3825581.jpeg?w=400&h=300&fit=crop",
      progress: 75,
      collaborators: []
    },
    {
      id: 3,
      title: "Complete Guide to Digital Transformation",
      type: "ebook",
      status: "completed",
      createdAt: "2024-01-13T09:15:00Z",
      pageCount: 120,
      thumbnail: "https://images.pixabay.com/photo/2016/11/29/06/15/book-1867171_1280.jpg?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Alex Rivera", email: "<EMAIL>" }
      ]
    },
    {
      id: 4,
      title: "Business Proposal: Green Energy Solutions",
      type: "business",
      status: "shared",
      createdAt: "2024-01-12T16:45:00Z",
      pageCount: 32,
      thumbnail: "https://images.unsplash.com/photo-1497435334941-8c899ee9e8e9?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: [
        { name: "Emma Wilson", email: "<EMAIL>" },
        { name: "David Park", email: "<EMAIL>" },
        { name: "Lisa Zhang", email: "<EMAIL>" }
      ]
    },
    {
      id: 5,
      title: "Thesis: Sustainable Urban Development",
      type: "academic",
      status: "draft",
      createdAt: "2024-01-11T11:30:00Z",
      pageCount: 85,
      thumbnail: "https://images.pexels.com/photos/3184291/pexels-photo-3184291.jpeg?w=400&h=300&fit=crop",
      progress: 60,
      collaborators: []
    },
    {
      id: 6,
      title: "The Future of Remote Work - eBook",
      type: "ebook",
      status: "completed",
      createdAt: "2024-01-10T13:20:00Z",
      pageCount: 95,
      thumbnail: "https://images.pixabay.com/photo/2020/07/08/04/12/work-5382501_1280.jpg?w=400&h=300&fit=crop",
      progress: 100,
      collaborators: []
    }
  ];

  const mockRecentTemplates = [
    {
      id: 1,
      name: "Research Paper Template",
      category: "Academic",
      icon: "FileText"
    },
    {
      id: 2,
      name: "Business Report Template",
      category: "Business",
      icon: "BarChart3"
    },
    {
      id: 3,
      name: "eBook Template",
      category: "Publishing",
      icon: "Book"
    }
  ];

  const mockCollaborationNotifications = [
    {
      id: 1,
      inviterName: "Sarah Johnson",
      documentTitle: "Market Analysis Report Q1 2024",
      timestamp: "2024-01-15T08:30:00Z",
      type: "collaboration_invite"
    },
    {
      id: 2,
      inviterName: "Dr. Michael Chen",
      documentTitle: "Research Methodology Guidelines",
      timestamp: "2024-01-14T15:45:00Z",
      type: "collaboration_invite"
    }
  ];

  const mockActivityFeed = [
    {
      id: 1,
      type: "document_created",
      description: "Created new business document 'Marketing Strategy'",
      timestamp: "2024-01-15T10:30:00Z"
    },
    {
      id: 2,
      type: "collaboration_invite",
      description: "Sarah Johnson invited you to collaborate",
      timestamp: "2024-01-15T08:30:00Z"
    },
    {
      id: 3,
      type: "plagiarism_check",
      description: "Completed plagiarism check for 'Research Paper'",
      timestamp: "2024-01-14T16:20:00Z"
    },
    {
      id: 4,
      type: "template_used",
      description: "Used \'Academic Paper\' template",
      timestamp: "2024-01-14T14:15:00Z"
    },
    {
      id: 5,
      type: "document_shared",
      description: "Shared \'Business Proposal\' with team",
      timestamp: "2024-01-13T12:45:00Z"
    }
  ];

  const mockUsageHistory = [
    { activity: "Document Generation", credits: 15, icon: "FileText" },
    { activity: "AI Content Enhancement", credits: 8, icon: "Wand2" },
    { activity: "Plagiarism Check", credits: 5, icon: "Shield" },
    { activity: "Template Customization", credits: 3, icon: "Settings" }
  ];

  // Stats calculations
  const totalDocuments = mockDocuments.length;
  const draftDocuments = mockDocuments.filter(doc => doc.status === 'draft').length;
  const completedDocuments = mockDocuments.filter(doc => doc.status === 'completed').length;
  const sharedDocuments = mockDocuments.filter(doc => doc.status === 'shared').length;
  const collaborationInvites = mockCollaborationNotifications.length;
  const plagiarismScans = mockActivityFeed.filter(activity => activity.type === 'plagiarism_check').length;

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  const handleEditDocument = (document) => {
    navigate('/document-creator', { state: { documentId: document.id } });
  };

  const handleDuplicateDocument = (document) => {
    console.log('Duplicating document:', document.title);
    // Implement duplication logic
  };

  const handleShareDocument = (document) => {
    console.log('Sharing document:', document.title);
    navigate('/collaboration-workspace', { state: { documentId: document.id } });
  };

  const handleDeleteDocument = (document) => {
    console.log('Deleting document:', document.title);
    // Implement deletion logic with confirmation
  };

  const handleAcceptCollaboration = async (notification) => {
    console.log('Accepting collaboration:', notification.documentTitle);
    // Implement accept logic
    return Promise.resolve();
  };

  const handleDeclineCollaboration = async (notification) => {
    console.log('Declining collaboration:', notification.documentTitle);
    // Implement decline logic
    return Promise.resolve();
  };

  const handleViewAllCollaborations = () => {
    navigate('/collaboration-workspace');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <QuickActionSidebar />
        <main className="lg:ml-72 pt-16">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div className="flex items-center justify-center h-64">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading your dashboard...</p>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      <QuickActionSidebar />
      
      <main className="lg:ml-72 pt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Breadcrumbs />
          
          {/* Welcome Section - Redesigned */}
          <div className="mb-12">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h1 className="text-4xl font-bold text-text-primary mb-3">
                  Welcome back, John! 👋
                </h1>
                <p className="text-lg text-text-secondary">
                  Ready to create something amazing today?
                </p>
              </div>
              <div className="hidden lg:flex items-center space-x-4">
                <button className="px-6 py-3 bg-gradient-to-r from-gradient-start to-gradient-end text-white rounded-xl font-semibold shadow-elevated hover:shadow-hero transition-all duration-300 transform hover:-translate-y-1">
                  <Icon name="Plus" size={20} className="mr-2" />
                  Create Document
                </button>
              </div>
            </div>
          </div>

          {/* Quick Stats - Redesigned */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div className="bg-white rounded-xl p-6 shadow-card border border-border hover:shadow-elevated transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-primary-light rounded-lg flex items-center justify-center">
                  <Icon name="FileText" size={24} className="text-primary" />
                </div>
                <span className="text-xs font-medium text-success bg-success-light px-2 py-1 rounded-full">+12%</span>
              </div>
              <h3 className="text-2xl font-bold text-text-primary mb-1">{totalDocuments}</h3>
              <p className="text-sm text-text-secondary">Total Documents</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-card border border-border hover:shadow-elevated transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-accent-light rounded-lg flex items-center justify-center">
                  <Icon name="Users" size={24} className="text-accent" />
                </div>
                <span className="text-xs font-medium text-accent bg-accent-light px-2 py-1 rounded-full">+2 new</span>
              </div>
              <h3 className="text-2xl font-bold text-text-primary mb-1">{collaborationInvites}</h3>
              <p className="text-sm text-text-secondary">Collaboration Invites</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-card border border-border hover:shadow-elevated transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-warning-light rounded-lg flex items-center justify-center">
                  <Icon name="Shield" size={24} className="text-warning" />
                </div>
                <span className="text-xs font-medium text-warning bg-warning-light px-2 py-1 rounded-full">+5 this week</span>
              </div>
              <h3 className="text-2xl font-bold text-text-primary mb-1">{plagiarismScans}</h3>
              <p className="text-sm text-text-secondary">Plagiarism Scans</p>
            </div>

            <div className="bg-white rounded-xl p-6 shadow-card border border-border hover:shadow-elevated transition-all duration-300">
              <div className="flex items-center justify-between mb-4">
                <div className="w-12 h-12 bg-success-light rounded-lg flex items-center justify-center">
                  <Icon name="CheckCircle" size={24} className="text-success" />
                </div>
                <span className="text-xs font-medium text-success bg-success-light px-2 py-1 rounded-full">+3 this month</span>
              </div>
              <h3 className="text-2xl font-bold text-text-primary mb-1">{completedDocuments}</h3>
              <p className="text-sm text-text-secondary">Completed Documents</p>
            </div>
          </div>

          {/* AI Hero Section - Inspired by reference */}
          <div className="mb-12">
            <div className="bg-gradient-to-br from-gradient-start via-primary to-gradient-end rounded-2xl p-8 lg:p-12 text-white relative overflow-hidden">
              <div className="relative z-10">
                <div className="flex items-center mb-4">
                  <span className="bg-white/20 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium">
                    ✨ AI-Powered
                  </span>
                </div>
                <h2 className="text-3xl lg:text-4xl font-bold mb-4">
                  Meet your AI-powered document creator
                </h2>
                <p className="text-lg text-white/90 mb-8 max-w-2xl">
                  Transform your ideas into professional documents with our advanced AI technology.
                  Create, collaborate, and perfect your content effortlessly.
                </p>
                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    onClick={() => navigate('/document-creator')}
                    className="bg-white text-primary px-8 py-4 rounded-xl font-semibold hover:bg-white/90 transition-all duration-300 transform hover:-translate-y-1 shadow-elevated"
                  >
                    Try it now →
                  </button>
                  <button
                    onClick={() => navigate('/template-library')}
                    className="bg-white/10 backdrop-blur-sm border border-white/20 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/20 transition-all duration-300"
                  >
                    Browse Templates
                  </button>
                </div>
              </div>

              {/* Decorative elements */}
              <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
              <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
            </div>
          </div>

          {/* Quick Creation Options */}
          <div className="mb-12">
            <h3 className="text-xl font-semibold text-text-primary mb-6">Quick Start Options</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {[
                { icon: 'FileText', label: 'Blank Document', color: 'primary' },
                { icon: 'Book', label: 'eBook', color: 'accent' },
                { icon: 'GraduationCap', label: 'Academic Paper', color: 'success' },
                { icon: 'Briefcase', label: 'Business Report', color: 'warning' },
                { icon: 'Upload', label: 'Import File', color: 'secondary' },
                { icon: 'Template', label: 'From Template', color: 'primary' },
              ].map((option, index) => (
                <div key={index} className="bg-white rounded-xl p-4 shadow-card border border-border hover:shadow-elevated transition-all duration-300 cursor-pointer group">
                  <div className={`w-12 h-12 bg-${option.color}-light rounded-lg flex items-center justify-center mb-3 group-hover:scale-110 transition-transform duration-300`}>
                    <Icon name={option.icon} size={20} className={`text-${option.color}`} />
                  </div>
                  <p className="text-sm font-medium text-text-primary">{option.label}</p>
                </div>
              ))}
            </div>
          </div>

          {/* Main Content Grid - Restructured to 3-column */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Recent Documents - Takes 2 columns */}
            <div className="lg:col-span-2">
              <div className="flex items-center justify-between mb-6">
                <h3 className="text-xl font-semibold text-text-primary">Recent Documents</h3>
                <button
                  onClick={() => navigate('/dashboard')}
                  className="text-primary hover:text-primary/80 font-medium text-sm transition-colors duration-300"
                >
                  View all →
                </button>
              </div>

              {/* Document Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockDocuments.slice(0, 4).map((document) => (
                  <div key={document.id} className="bg-white rounded-xl p-6 shadow-card border border-border hover:shadow-elevated transition-all duration-300 group cursor-pointer">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h4 className="font-semibold text-text-primary group-hover:text-primary transition-colors duration-300 line-clamp-2">
                          {document.title}
                        </h4>
                        <p className="text-sm text-text-secondary mt-1 capitalize">{document.type}</p>
                      </div>
                      <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                        document.status === 'completed' ? 'bg-success-light text-success' :
                        document.status === 'draft' ? 'bg-warning-light text-warning' :
                        'bg-primary-light text-primary'
                      }`}>
                        {document.status}
                      </div>
                    </div>

                    <div className="flex items-center justify-between text-sm text-text-secondary">
                      <span>{document.pageCount} pages</span>
                      <span>{new Date(document.createdAt).toLocaleDateString()}</span>
                    </div>

                    {document.progress < 100 && (
                      <div className="mt-3">
                        <div className="flex items-center justify-between text-xs text-text-secondary mb-1">
                          <span>Progress</span>
                          <span>{document.progress}%</span>
                        </div>
                        <div className="w-full bg-surface-secondary rounded-full h-2">
                          <div
                            className="bg-primary h-2 rounded-full transition-all duration-300"
                            style={{ width: `${document.progress}%` }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {document.collaborators.length > 0 && (
                      <div className="flex items-center mt-3 pt-3 border-t border-border">
                        <div className="flex -space-x-2">
                          {document.collaborators.slice(0, 3).map((collaborator, index) => (
                            <div key={index} className="w-6 h-6 bg-primary-light rounded-full flex items-center justify-center text-xs font-medium text-primary border-2 border-white">
                              {collaborator.name.charAt(0)}
                            </div>
                          ))}
                        </div>
                        <span className="text-xs text-text-secondary ml-2">
                          {document.collaborators.length} collaborator{document.collaborators.length !== 1 ? 's' : ''}
                        </span>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Activity & Notifications Sidebar - Takes 1 column */}
            <div className="space-y-6">
              {/* Activity Feed */}
              <div className="bg-white rounded-xl p-6 shadow-card border border-border">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-text-primary">Recent Activity</h3>
                  <button className="text-primary hover:text-primary/80 text-sm font-medium">View all</button>
                </div>
                <div className="space-y-3">
                  {mockActivityFeed.slice(0, 4).map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3">
                      <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
                        activity.type === 'document_created' ? 'bg-primary-light' :
                        activity.type === 'collaboration_invite' ? 'bg-accent-light' :
                        activity.type === 'plagiarism_check' ? 'bg-warning-light' :
                        'bg-success-light'
                      }`}>
                        <Icon
                          name={
                            activity.type === 'document_created' ? 'FileText' :
                            activity.type === 'collaboration_invite' ? 'Users' :
                            activity.type === 'plagiarism_check' ? 'Shield' :
                            'CheckCircle'
                          }
                          size="sm"
                          variant={
                            activity.type === 'document_created' ? 'primary' :
                            activity.type === 'collaboration_invite' ? 'accent' :
                            activity.type === 'plagiarism_check' ? 'warning' :
                            'success'
                          }
                        />
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm text-text-primary">{activity.description}</p>
                        <p className="text-xs text-text-secondary mt-1">
                          {new Date(activity.timestamp).toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Collaboration Notifications */}
              {mockCollaborationNotifications.length > 0 && (
                <div className="bg-white rounded-xl p-6 shadow-card border border-border">
                  <h3 className="text-lg font-semibold text-text-primary mb-4">Collaboration Invites</h3>
                  <div className="space-y-3">
                    {mockCollaborationNotifications.map((notification) => (
                      <div key={notification.id} className="p-3 bg-surface-secondary rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <p className="text-sm font-medium text-text-primary">{notification.inviterName}</p>
                          <span className="text-xs text-text-secondary">
                            {new Date(notification.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                        <p className="text-sm text-text-secondary mb-3">{notification.documentTitle}</p>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleAcceptCollaboration(notification)}
                            className="px-3 py-1 bg-primary text-white text-xs rounded-lg hover:bg-primary/90 transition-colors duration-300"
                          >
                            Accept
                          </button>
                          <button
                            onClick={() => handleDeclineCollaboration(notification)}
                            className="px-3 py-1 bg-surface-hover text-text-secondary text-xs rounded-lg hover:bg-border-strong transition-colors duration-300"
                          >
                            Decline
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Credit Usage - Simplified */}
              <div className="bg-white rounded-xl p-6 shadow-card border border-border">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-text-primary">Credits</h3>
                  <Icon name="Zap" size="sm" variant="warning" />
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-text-secondary">Used</span>
                    <span className="text-sm font-medium text-text-primary">250 / 500</span>
                  </div>
                  <div className="w-full bg-surface-secondary rounded-full h-2">
                    <div className="bg-gradient-to-r from-primary to-accent h-2 rounded-full" style={{ width: '50%' }}></div>
                  </div>
                  <button className="w-full text-sm text-primary hover:text-primary/80 font-medium">
                    Upgrade Plan →
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Dashboard;