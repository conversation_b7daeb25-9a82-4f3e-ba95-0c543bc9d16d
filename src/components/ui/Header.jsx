import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';
import Input from './Input';

const Header = () => {
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const navigationItems = [
    { label: 'Dashboard', path: '/dashboard', icon: 'LayoutDashboard' },
    { label: 'Create', path: '/document-creator', icon: 'FileText' },
    { label: 'Templates', path: '/template-library', icon: 'Library' },
    { label: 'Verify', path: '/plagiarism-checker', icon: 'Shield' },
    { label: 'Collaborate', path: '/collaboration-workspace', icon: 'Users' },
  ];

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      console.log('Searching for:', searchQuery);
      setIsSearchOpen(false);
      setSearchQuery('');
    }
  };

  const handleNavigation = (path) => {
    navigate(path);
    setIsMobileMenuOpen(false);
  };

  const handleProfileClick = () => {
    setIsProfileOpen(!isProfileOpen);
  };

  const handleAccountSettings = () => {
    navigate('/account-settings');
    setIsProfileOpen(false);
  };

  const handleLogout = () => {
    console.log('Logging out...');
    setIsProfileOpen(false);
  };

  return (
    <header className="fixed top-0 left-0 right-0 z-1000 bg-white border-b border-border shadow-card backdrop-blur-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo and Brand */}
          <div className="flex items-center space-x-8">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-lg flex items-center justify-center shadow-soft">
                <Icon name="FileText" size={20} color="white" />
              </div>
              <span className="text-xl font-heading font-bold text-text-primary">
                DocForge AI
              </span>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden lg:flex items-center space-x-1">
              {navigationItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => handleNavigation(item.path)}
                  className={`px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 flex items-center space-x-2 ${
                    location.pathname === item.path
                      ? 'bg-primary text-white shadow-card'
                      : 'text-text-secondary hover:text-text-primary hover:bg-surface-hover'
                  }`}
                >
                  <Icon name={item.icon} size={16} />
                  <span>{item.label}</span>
                </button>
              ))}
            </nav>
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-4">
            {/* Credits Display */}
            <div className="hidden sm:flex items-center space-x-2 px-3 py-1.5 bg-gradient-to-r from-warning-light to-accent-light rounded-xl">
              <Icon name="Zap" size={16} variant="warning" />
              <span className="text-sm font-medium text-warning">250 Credits</span>
            </div>

            {/* Search */}
            <div className="relative">
              {isSearchOpen ? (
                <form onSubmit={handleSearch} className="flex items-center">
                  <Input
                    type="search"
                    placeholder="Search documents..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-64 h-9"
                    autoFocus
                  />
                  <Button
                    variant="ghost"
                    onClick={() => setIsSearchOpen(false)}
                    className="ml-2 p-1.5"
                  >
                    <Icon name="X" size={16} />
                  </Button>
                </form>
              ) : (
                <Button
                  variant="ghost"
                  onClick={() => setIsSearchOpen(true)}
                  className="p-2"
                >
                  <Icon name="Search" size={18} />
                </Button>
              )}
            </div>

            {/* Notifications */}
            <Button variant="ghost" className="p-2 relative">
              <Icon name="Bell" size={18} />
              <span className="absolute -top-1 -right-1 w-2 h-2 bg-error rounded-full"></span>
            </Button>

            {/* Profile Dropdown */}
            <div className="relative">
              <Button
                variant="ghost"
                onClick={handleProfileClick}
                className="p-1.5 rounded-full"
              >
                <div className="w-8 h-8 bg-secondary rounded-full flex items-center justify-center">
                  <Icon name="User" size={16} color="white" />
                </div>
              </Button>

              {isProfileOpen && (
                <div className="absolute right-0 mt-2 w-48 bg-surface rounded-lg shadow-elevation-3 border border-border z-1100">
                  <div className="py-1">
                    <div className="px-4 py-2 border-b border-border">
                      <p className="text-sm font-medium text-text-primary">John Doe</p>
                      <p className="text-xs text-text-secondary"><EMAIL></p>
                    </div>
                    <button
                      onClick={handleAccountSettings}
                      className="w-full text-left px-4 py-2 text-sm text-text-primary hover:bg-background flex items-center space-x-2"
                    >
                      <Icon name="Settings" size={16} />
                      <span>Account Settings</span>
                    </button>
                    <button
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-error hover:bg-background flex items-center space-x-2"
                    >
                      <Icon name="LogOut" size={16} />
                      <span>Sign Out</span>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
              className="lg:hidden p-2"
            >
              <Icon name={isMobileMenuOpen ? "X" : "Menu"} size={20} />
            </Button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="lg:hidden border-t border-border bg-surface">
            <nav className="py-4 space-y-1">
              {navigationItems.map((item) => (
                <button
                  key={item.path}
                  onClick={() => handleNavigation(item.path)}
                  className={`w-full text-left px-4 py-3 text-sm font-medium transition-micro flex items-center space-x-3 ${
                    location.pathname === item.path
                      ? 'bg-primary text-primary-foreground'
                      : 'text-text-secondary hover:text-text-primary hover:bg-background'
                  }`}
                >
                  <Icon name={item.icon} size={18} />
                  <span>{item.label}</span>
                </button>
              ))}
              <div className="px-4 py-3 border-t border-border mt-2">
                <div className="flex items-center space-x-2 text-sm">
                  <Icon name="Zap" size={16} color="var(--color-accent)" />
                  <span className="text-accent font-medium">250 Credits Available</span>
                </div>
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;