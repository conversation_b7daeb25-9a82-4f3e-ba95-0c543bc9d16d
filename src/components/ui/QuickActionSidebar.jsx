import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import Icon from '../AppIcon';
import Button from './Button';

const QuickActionSidebar = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const getContextualActions = () => {
    switch (location.pathname) {
      case '/dashboard':
        return [
          {
            title: 'Quick Actions',
            actions: [
              { label: 'New Document', icon: 'Plus', action: () => navigate('/document-creator') },
              { label: 'Browse Templates', icon: 'Library', action: () => navigate('/template-library') },
              { label: 'Check Plagiarism', icon: 'Shield', action: () => navigate('/plagiarism-checker') },
            ]
          },
          {
            title: 'Recent Templates',
            actions: [
              { label: 'Research Paper', icon: 'FileText', action: () => console.log('Research Paper template') },
              { label: 'Business Report', icon: 'BarChart3', action: () => console.log('Business Report template') },
              { label: 'Essay Format', icon: 'BookOpen', action: () => console.log('Essay Format template') },
            ]
          },
          {
            title: 'Collaboration',
            actions: [
              { label: 'Shared Documents', icon: 'Users', action: () => navigate('/collaboration-workspace') },
              { label: 'Team Invites', icon: 'UserPlus', badge: '2', action: () => console.log('Team invites') },
            ]
          }
        ];

      case '/document-creator':
        return [
          {
            title: 'Document Tools',
            actions: [
              { label: 'Save Document', icon: 'Save', action: () => console.log('Save document') },
              { label: 'Export PDF', icon: 'Download', action: () => console.log('Export PDF') },
              { label: 'Share Document', icon: 'Share2', action: () => console.log('Share document') },
            ]
          },
          {
            title: 'AI Assistance',
            actions: [
              { label: 'Grammar Check', icon: 'CheckCircle', action: () => console.log('Grammar check') },
              { label: 'Improve Writing', icon: 'Wand2', action: () => console.log('Improve writing') },
              { label: 'Generate Content', icon: 'Sparkles', action: () => console.log('Generate content') },
            ]
          },
          {
            title: 'Formatting',
            actions: [
              { label: 'Headings', icon: 'Heading', action: () => console.log('Apply headings') },
              { label: 'Citations', icon: 'Quote', action: () => console.log('Add citations') },
              { label: 'References', icon: 'BookMarked', action: () => console.log('Manage references') },
            ]
          }
        ];

      case '/template-library':
        return [
          {
            title: 'Template Categories',
            actions: [
              { label: 'Academic Papers', icon: 'GraduationCap', action: () => console.log('Academic templates') },
              { label: 'Business Documents', icon: 'Briefcase', action: () => console.log('Business templates') },
              { label: 'Creative Writing', icon: 'Feather', action: () => console.log('Creative templates') },
            ]
          },
          {
            title: 'My Templates',
            actions: [
              { label: 'Saved Templates', icon: 'Heart', action: () => console.log('Saved templates') },
              { label: 'Custom Templates', icon: 'Settings', action: () => console.log('Custom templates') },
            ]
          }
        ];

      case '/plagiarism-checker':
        return [
          {
            title: 'Check Options',
            actions: [
              { label: 'Upload Document', icon: 'Upload', action: () => console.log('Upload document') },
              { label: 'Paste Text', icon: 'Clipboard', action: () => console.log('Paste text') },
              { label: 'Check URL', icon: 'Link', action: () => console.log('Check URL') },
            ]
          },
          {
            title: 'Recent Checks',
            actions: [
              { label: 'Research Paper.docx', icon: 'FileText', action: () => console.log('View report') },
              { label: 'Essay Draft.pdf', icon: 'File', action: () => console.log('View report') },
            ]
          }
        ];

      case '/collaboration-workspace':
        return [
          {
            title: 'Workspace Actions',
            actions: [
              { label: 'Invite Members', icon: 'UserPlus', action: () => console.log('Invite members') },
              { label: 'Create Project', icon: 'FolderPlus', action: () => console.log('Create project') },
              { label: 'Schedule Meeting', icon: 'Calendar', action: () => console.log('Schedule meeting') },
            ]
          },
          {
            title: 'Active Projects',
            actions: [
              { label: 'Research Collaboration', icon: 'Users', badge: '3', action: () => console.log('Open project') },
              { label: 'Team Report', icon: 'FileText', badge: '1', action: () => console.log('Open project') },
            ]
          }
        ];

      default:
        return [
          {
            title: 'Quick Navigation',
            actions: [
              { label: 'Dashboard', icon: 'LayoutDashboard', action: () => navigate('/dashboard') },
              { label: 'Create Document', icon: 'FileText', action: () => navigate('/document-creator') },
              { label: 'Templates', icon: 'Library', action: () => navigate('/template-library') },
            ]
          }
        ];
    }
  };

  const contextualActions = getContextualActions();

  return (
    <aside className={`fixed left-0 top-16 h-[calc(100vh-4rem)] bg-white border-r border-border z-1000 transition-all duration-300 shadow-card ${
      isCollapsed ? 'w-16' : 'w-72'
    } lg:block hidden`}>
      <div className="flex flex-col h-full">
        {/* Collapse Toggle */}
        <div className="p-4 border-b border-border">
          <button
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="w-full flex items-center justify-start p-2 text-text-secondary hover:text-text-primary hover:bg-surface-hover rounded-xl transition-all duration-300"
          >
            <Icon name={isCollapsed ? "ChevronRight" : "ChevronLeft"} size={16} />
            {!isCollapsed && <span className="ml-2 text-sm font-medium">Collapse</span>}
          </button>
        </div>

        {/* Actions Content */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {contextualActions.map((section, sectionIndex) => (
            <div key={sectionIndex}>
              {!isCollapsed && (
                <h3 className="text-xs font-semibold text-text-secondary uppercase tracking-wider mb-3">
                  {section.title}
                </h3>
              )}
              <div className="space-y-1">
                {section.actions.map((action, actionIndex) => (
                  <button
                    key={actionIndex}
                    onClick={action.action}
                    className={`w-full flex items-center space-x-3 p-3 rounded-xl text-sm text-text-secondary hover:text-text-primary hover:bg-surface-hover transition-all duration-300 group ${
                      isCollapsed ? 'justify-center' : 'justify-start'
                    }`}
                    title={isCollapsed ? action.label : ''}
                  >
                    <Icon name={action.icon} size={16} className="flex-shrink-0" />
                    {!isCollapsed && (
                      <>
                        <span className="flex-1 text-left font-medium">{action.label}</span>
                        {action.badge && (
                          <span className="bg-accent-light text-accent text-xs px-2 py-1 rounded-full font-medium">
                            {action.badge}
                          </span>
                        )}
                      </>
                    )}
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Credits Display */}
        <div className="p-4 border-t border-border">
          <div className={`flex items-center space-x-3 p-3 bg-gradient-to-r from-warning-light to-accent-light rounded-xl ${
            isCollapsed ? 'justify-center' : ''
          }`}>
            <Icon name="Zap" size={16} variant="warning" />
            {!isCollapsed && (
              <div className="flex-1">
                <p className="text-sm font-semibold text-warning">250 Credits</p>
                <p className="text-xs text-text-secondary">Available</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </aside>
  );
};

export default QuickActionSidebar;