/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Primary Colors - Inspired by the reference design
        'primary': '#2563EB', // Modern blue
        'primary-foreground': '#FFFFFF',
        'primary-light': '#DBEAFE', // Light blue for backgrounds

        // Secondary Colors
        'secondary': '#64748B', // Slate gray
        'secondary-foreground': '#FFFFFF',

        // Accent Colors
        'accent': '#8B5CF6', // Purple accent
        'accent-foreground': '#FFFFFF',
        'accent-light': '#F3E8FF',

        // Background Colors - Cleaner, more modern
        'background': '#FEFEFE', // Pure white background
        'surface': '#FFFFFF',
        'surface-secondary': '#F8FAFC', // Very light gray
        'surface-hover': '#F1F5F9', // Hover states

        // Text Colors - Better contrast
        'text-primary': '#0F172A', // Darker for better readability
        'text-secondary': '#64748B', // Softer secondary text
        'text-muted': '#94A3B8', // Even lighter for less important text

        // Status Colors
        'success': '#10B981',
        'success-light': '#D1FAE5',
        'warning': '#F59E0B',
        'warning-light': '#FEF3C7',
        'error': '#EF4444',
        'error-light': '#FEE2E2',

        // Border Colors - Subtler
        'border': '#F1F5F9',
        'border-strong': '#E2E8F0',

        // Gradient Colors
        'gradient-start': '#667EEA',
        'gradient-end': '#764BA2',
      },
      fontFamily: {
        'sans': ['Inter', 'system-ui', 'sans-serif'],
        'heading': ['Inter', 'system-ui', 'sans-serif'],
        'body': ['Inter', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'monospace'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1rem' }],
        'sm': ['0.875rem', { lineHeight: '1.25rem' }],
        'base': ['1rem', { lineHeight: '1.5rem' }],
        'lg': ['1.125rem', { lineHeight: '1.75rem' }],
        'xl': ['1.25rem', { lineHeight: '1.75rem' }],
        '2xl': ['1.5rem', { lineHeight: '2rem' }],
        '3xl': ['1.875rem', { lineHeight: '2.25rem' }],
        '4xl': ['2.25rem', { lineHeight: '2.5rem' }],
      },
      fontWeight: {
        'light': '300',
        'normal': '400',
        'medium': '500',
        'semibold': '600',
        'bold': '700',
        'extrabold': '800',
      },
      boxShadow: {
        'soft': '0 1px 3px rgba(0, 0, 0, 0.05)',
        'card': '0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03)',
        'elevated': '0 10px 15px -3px rgba(0, 0, 0, 0.08), 0 4px 6px -2px rgba(0, 0, 0, 0.03)',
        'hero': '0 25px 50px -12px rgba(0, 0, 0, 0.15)',
        'inner-soft': 'inset 0 1px 2px rgba(0, 0, 0, 0.05)',
      },
      borderRadius: {
        'none': '0',
        'sm': '0.375rem',
        'DEFAULT': '0.5rem',
        'md': '0.75rem',
        'lg': '1rem',
        'xl': '1.5rem',
        '2xl': '2rem',
        'full': '9999px',
      },
      animation: {
        'fade-in': 'fadeIn 200ms ease-out',
        'slide-up': 'slideUp 300ms ease-out',
        'scale-in': 'scaleIn 150ms ease-in-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
      },
      zIndex: {
        '1000': '1000',
        '1050': '1050',
        '1100': '1100',
        '1200': '1200',
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('tailwindcss-animate'),
  ],
}